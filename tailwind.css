@tailwind components;
@tailwind utilities;

@layer components {
  .all-\[unset\] {
    all: unset;
  }
}

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 47.4% 11.2%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;

    --card: transparent;
    --card-foreground: 222.2 47.4% 11.2%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 100% 50%;
    --destructive-foreground: 210 40% 98%;

    --ring: 215 20.2% 65.1%;

    --radius: 0.5rem;
  }

  .dark {
    --background: 224 71% 4%;
    --foreground: 213 31% 91%;

    --muted: 223 47% 11%;
    --muted-foreground: 215.4 16.3% 56.9%;

    --accent: 216 34% 17%;
    --accent-foreground: 210 40% 98%;

    --popover: 224 71% 4%;
    --popover-foreground: 215 20.2% 65.1%;

    --border: 216 34% 17%;
    --input: 216 34% 17%;

    --card: transparent;
    --card-foreground: 213 31% 91%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 1.2%;

    --secondary: 222.2 47.4% 11.2%;
    --secondary-foreground: 210 40% 98%;

    --destructive: 0 63% 31%;
    --destructive-foreground: 210 40% 98%;

    --ring: 216 34% 17%;

    --radius: 0.5rem;
  }
}

@layer base {
  * {
    @apply border-border;
    cursor: url('/cursor.svg') 2 2, url('/cursor.cur') 2 2, auto;
  }

  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
    cursor: url('/cursor.svg') 2 2, url('/cursor.cur') 2 2, auto;
  }

  /* Custom cursor for different interactive elements */
  button, a, input, textarea, select, [role="button"], [tabindex]:not([tabindex="-1"]) {
    cursor: url('/cursor.svg') 2 2, url('/cursor.cur') 2 2, pointer;
  }

  /* Hover states for interactive elements */
  button:hover, a:hover, input:hover, textarea:hover, select:hover, [role="button"]:hover {
    cursor: url('/cursor.svg') 2 2, url('/cursor.cur') 2 2, pointer;
    transform: scale(1.05);
    transition: transform 0.1s ease;
  }

  /* Text selection cursor for inputs */
  input[type="text"]:focus, input[type="email"]:focus, input[type="password"]:focus, textarea:focus {
    cursor: text;
  }

  /* Disabled elements */
  button:disabled, input:disabled, textarea:disabled, select:disabled {
    cursor: not-allowed;
    opacity: 0.6;
  }

  /* Special cursor for draggable elements */
  [draggable="true"] {
    cursor: url('/cursor.svg') 2 2, url('/cursor.cur') 2 2, move;
  }

  /* Loading cursor */
  .loading {
    cursor: url('/cursor.svg') 2 2, url('/cursor.cur') 2 2, wait;
  }

  /* Custom scrollbar styling */
  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    background: transparent;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    background-color: #8b2635;
    border-radius: 3px;
  }

  .scrollbar-thin:hover::-webkit-scrollbar-thumb {
    background-color: #6d1f2c;
  }

  /* Tiptap Editor Styles */
  .ProseMirror {
    outline: none;
    font-family: 'Poppins', sans-serif;
    line-height: 1.6;
  }

  .ProseMirror h1 {
    font-size: 2rem;
    font-weight: 700;
    margin: 1.5rem 0 1rem 0;
    color: #2E0406;
    font-family: 'Cormorant', serif;
  }

  .ProseMirror h2 {
    font-size: 1.5rem;
    font-weight: 600;
    margin: 1.25rem 0 0.75rem 0;
    color: #2E0406;
    font-family: 'Cormorant', serif;
  }

  .ProseMirror h3 {
    font-size: 1.25rem;
    font-weight: 600;
    margin: 1rem 0 0.5rem 0;
    color: #2E0406;
    font-family: 'Cormorant', serif;
  }

  .ProseMirror p {
    margin: 0.75rem 0;
    color: #2E0406;
  }

  .ProseMirror ul, .ProseMirror ol {
    margin: 0.75rem 0;
    padding-left: 1.5rem;
  }

  .ProseMirror li {
    margin: 0.25rem 0;
    color: #2E0406;
  }

  .ProseMirror blockquote {
    border-left: 4px solid #8b2635;
    padding-left: 1rem;
    margin: 1rem 0;
    font-style: italic;
    color: #6d1f2c;
    background: #f5f0e8;
    border-radius: 0 0.5rem 0.5rem 0;
  }

  .ProseMirror code {
    background: #f5f0e8;
    padding: 0.125rem 0.25rem;
    border-radius: 0.25rem;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.875rem;
    color: #8b2635;
  }

  .ProseMirror pre {
    background: #f5f0e8;
    padding: 1rem;
    border-radius: 0.5rem;
    overflow-x: auto;
    margin: 1rem 0;
    border: 1px solid #e0d7cc;
  }

  .ProseMirror pre code {
    background: transparent;
    padding: 0;
    color: #2E0406;
  }

  .ProseMirror a {
    color: #8b2635;
    text-decoration: underline;
    transition: color 0.2s ease;
  }

  .ProseMirror a:hover {
    color: #6d1f2c;
  }

  .ProseMirror img {
    max-width: 100%;
    height: auto;
    border-radius: 0.5rem;
    margin: 1rem 0;
  }

  .ProseMirror strong {
    font-weight: 600;
    color: #2E0406;
  }

  .ProseMirror em {
    font-style: italic;
    color: #6d1f2c;
  }

  .ProseMirror s {
    text-decoration: line-through;
    color: #8b7355;
  }

  /* Placeholder styling */
  .ProseMirror p.is-editor-empty:first-child::before {
    content: attr(data-placeholder);
    float: left;
    color: #8b7355;
    pointer-events: none;
    height: 0;
  }
}
