import React, { useState, useRef, useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { Button } from "../../components/ui/button";
import { useAuth } from "../../hooks/useAuth";
import { LoadingScreen } from "../../components/ui/loading-screen";
import { supabase } from "../../lib/supabase";
import {
  Home as HomeIcon,
  Brain,
  Map,
  Trophy,
  LogOut,
  Search,
  Bell,
  User,
  X,
  Menu,
  ChevronRight,
  Plus,
  FileText,
  Edit3,
  MoreVertical,
  Pin,
  Trash2,
  Folder,
  FolderOpen,
  ChevronDown,
  FolderPlus,
  Code,
  Image,
  Video,
  Music,
  File,
  Archive,
  FileArchive,
  FileSpreadsheet,
  FileImage,
  FileVideo,
  FileAudio,
  Database,
  Presentation,
  AlertTriangle
} from "lucide-react";
import { AddFolderModal } from "../../components/AddFolderModal";
import { VaultSearch, FilterType } from "../../components/VaultSearch/VaultSearch";
import { Tabs, TabsList, TabsTrigger, TabsContent } from "../../components/ui/tabs";
import { NoteEditorDrawer } from "../../components/NoteEditorDrawer";
import Fuse from 'fuse.js';

interface VaultBlock {
  id: string;
  user_id: string;
  parent_id?: string;
  name: string;
  smart_tag: string;
  banner_gradient: string;
  custom_banner_image?: string;
  is_pinned: boolean;
  total_items: number;
  position: number;
  depth: number;
  created_at: string;
  updated_at: string;
}

interface BlockContent {
  id: string;
  block_id: string;
  title: string;
  content: string;
  content_type: 'text' | 'image' | 'video' | 'audio' | 'file' | 'archive';
  position: number;
  created_at: string;
  updated_at: string;
}

export const BlockContent = (): JSX.Element => {
  const { blockId } = useParams<{ blockId: string }>();
  const navigate = useNavigate();
  const { user, logout } = useAuth();
  
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [loading, setLoading] = useState(true);
  const [logoutLoading, setLogoutLoading] = useState(false);
  const [currentBlock, setCurrentBlock] = useState<VaultBlock | null>(null);
  const [breadcrumb, setBreadcrumb] = useState<VaultBlock[]>([]);
  const [subBlocks, setSubBlocks] = useState<VaultBlock[]>([]);
  const [blockContents, setBlockContents] = useState<BlockContent[]>([]);
  const [showAddBlockModal, setShowAddBlockModal] = useState(false);
  const [showAddContentModal, setShowAddContentModal] = useState(false);
  const [showAddFolderModal, setShowAddFolderModal] = useState(false);
  const [showCreateDropdown, setShowCreateDropdown] = useState(false);

  // Search and filtering state
  const [searchQuery, setSearchQuery] = useState('');
  const [searchFilter, setSearchFilter] = useState<FilterType>('all');
  const [searchTag, setSearchTag] = useState('');
  const [filteredSubBlocks, setFilteredSubBlocks] = useState<VaultBlock[]>([]);
  const [filteredBlockContents, setFilteredBlockContents] = useState<BlockContent[]>([]);

  // Combined content for unified grid
  const [filteredCombinedContent, setFilteredCombinedContent] = useState<(VaultBlock | BlockContent)[]>([]);

  // Sort state
  const [sortBy, setSortBy] = useState<'name' | 'date'>('date');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');

  // Draft notes state
  const [draftNotes, setDraftNotes] = useState<BlockContent[]>([]);

  // Subblock management state
  const [editingSubBlockId, setEditingSubBlockId] = useState<string | null>(null);
  const [editingSubBlockName, setEditingSubBlockName] = useState<string>('');
  const [openSubBlockMenuId, setOpenSubBlockMenuId] = useState<string | null>(null);

  // File management state
  const [editingFileId, setEditingFileId] = useState<string | null>(null);
  const [editingFileName, setEditingFileName] = useState<string>('');
  const [openFileMenuId, setOpenFileMenuId] = useState<string | null>(null);

  // Error notification state
  const [errorNotification, setErrorNotification] = useState<{
    show: boolean;
    title: string;
    message: string;
    type: 'error' | 'warning' | 'success';
  }>({
    show: false,
    title: '',
    message: '',
    type: 'error'
  });
  const [viewingFile, setViewingFile] = useState<BlockContent | null>(null);

  // File drop state
  const [isDragOver, setIsDragOver] = useState(false);

  // Editor drawer state
  const [isEditorOpen, setIsEditorOpen] = useState(false);
  const [editingDraft, setEditingDraft] = useState<BlockContent | null>(null);

  // Date formatting function
  const formatDate = (dateString: string): string => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));

    if (diffInHours < 1) return 'Just now';
    if (diffInHours < 24) return `${diffInHours} hour${diffInHours > 1 ? 's' : ''} ago`;

    // For items over 24 hours old, show dd/mm/yy hh:mm format
    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear().toString().slice(-2);
    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');

    return `${day}/${month}/${year} ${hours}:${minutes}`;
  };

  // Get file type icon based on content type or title extension
  const getFileTypeIcon = (content: BlockContent) => {
    const title = content.title?.toLowerCase() || '';
    const contentType = content.content_type || 'text';

    // Check by content type first
    if (contentType === 'image') {
      return { icon: FileImage, color: 'text-green-500', bgColor: 'bg-green-100' };
    }
    if (contentType === 'video') {
      return { icon: FileVideo, color: 'text-red-500', bgColor: 'bg-red-100' };
    }
    if (contentType === 'audio') {
      return { icon: FileAudio, color: 'text-purple-500', bgColor: 'bg-purple-100' };
    }
    if (contentType === 'archive') {
      return { icon: FileArchive, color: 'text-orange-500', bgColor: 'bg-orange-100' };
    }

    // Check by file extension
    if (title.includes('.')) {
      const extension = title.split('.').pop() || '';

      // Code files
      if (['js', 'jsx', 'ts', 'tsx', 'py', 'java', 'cpp', 'c', 'cs', 'php', 'rb', 'go', 'rs', 'swift', 'kt', 'html', 'css', 'scss', 'sass', 'less', 'json', 'xml', 'yaml', 'yml', 'sql', 'sh', 'bat', 'ps1'].includes(extension)) {
        return { icon: Code, color: 'text-blue-500', bgColor: 'bg-blue-100' };
      }

      // Document files
      if (['doc', 'docx', 'txt', 'rtf', 'odt'].includes(extension)) {
        return { icon: FileText, color: 'text-blue-600', bgColor: 'bg-blue-100' };
      }

      // PDF files
      if (['pdf'].includes(extension)) {
        return { icon: FileText, color: 'text-red-600', bgColor: 'bg-red-100' };
      }

      // Archive/Compressed files
      if (['zip', 'rar', '7z', 'tar', 'gz', 'bz2', 'xz', 'zipx', 'cab', 'iso', 'dmg'].includes(extension)) {
        return { icon: FileArchive, color: 'text-orange-500', bgColor: 'bg-orange-100' };
      }

      // Spreadsheet files
      if (['xls', 'xlsx', 'csv', 'ods', 'numbers'].includes(extension)) {
        return { icon: FileSpreadsheet, color: 'text-green-600', bgColor: 'bg-green-100' };
      }

      // Presentation files
      if (['ppt', 'pptx', 'odp', 'key'].includes(extension)) {
        return { icon: Presentation, color: 'text-orange-600', bgColor: 'bg-orange-100' };
      }

      // Database files
      if (['db', 'sqlite', 'sqlite3', 'mdb', 'accdb', 'dbf'].includes(extension)) {
        return { icon: Database, color: 'text-purple-600', bgColor: 'bg-purple-100' };
      }

      // Document files (expanded)
      if (['doc', 'docx', 'txt', 'rtf', 'odt', 'pages', 'md', 'tex'].includes(extension)) {
        return { icon: FileText, color: 'text-blue-600', bgColor: 'bg-blue-100' };
      }

      // Image files (expanded)
      if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp', 'ico', 'tiff', 'tif', 'raw', 'heic', 'heif'].includes(extension)) {
        return { icon: FileImage, color: 'text-green-500', bgColor: 'bg-green-100' };
      }

      // Video files (expanded)
      if (['mp4', 'avi', 'mkv', 'mov', 'wmv', 'flv', 'webm', 'm4v', '3gp', 'ogv', 'f4v'].includes(extension)) {
        return { icon: FileVideo, color: 'text-red-500', bgColor: 'bg-red-100' };
      }

      // Audio files (expanded)
      if (['mp3', 'wav', 'flac', 'aac', 'ogg', 'wma', 'm4a', 'opus', 'ape', 'ac3'].includes(extension)) {
        return { icon: FileAudio, color: 'text-purple-500', bgColor: 'bg-purple-100' };
      }

      // Executable files
      if (['exe', 'msi', 'app', 'deb', 'rpm', 'pkg', 'dmg', 'apk'].includes(extension)) {
        return { icon: File, color: 'text-gray-600', bgColor: 'bg-gray-100' };
      }
    }

    // Default to text file
    return { icon: FileText, color: 'text-gray-500', bgColor: 'bg-gray-100' };
  };

  // File drop handlers
  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.dataTransfer.types.includes('Files')) {
      setIsDragOver(true);
    }
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    // Only hide if we're leaving the drop zone container
    if (!e.currentTarget.contains(e.relatedTarget as Node)) {
      setIsDragOver(false);
    }
  };

  // File size validation (1GB = 1024 * 1024 * 1024 bytes)
  const MAX_FILE_SIZE = 1024 * 1024 * 1024; // 1GB

  // Format file size for display
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Show error notification
  const showErrorNotification = (title: string, message: string, type: 'error' | 'warning' | 'success' = 'error') => {
    setErrorNotification({
      show: true,
      title,
      message,
      type
    });

    // Auto-hide after 5 seconds
    setTimeout(() => {
      setErrorNotification(prev => ({ ...prev, show: false }));
    }, 5000);
  };

  const handleDrop = async (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragOver(false);

    if (!user || !currentBlock) return;

    console.log('Files dropped:', e.dataTransfer.files.length);
    console.log('User UID:', user.uid);
    console.log('Current block ID:', currentBlock.id);

    const files = Array.from(e.dataTransfer.files);
    const errors: string[] = [];
    let successCount = 0;

    for (const file of files) {
      try {
        // Validate file size
        if (file.size > MAX_FILE_SIZE) {
          const errorMsg = `File too large (${formatFileSize(file.size)}). Maximum size is 1GB.`;
          showErrorNotification('Upload Failed', `${file.name}: ${errorMsg}`, 'error');
          errors.push(`${file.name}: ${errorMsg}`);
          continue;
        }

        // Validate file name
        if (!file.name || file.name.trim() === '') {
          const errorMsg = 'Invalid file name detected.';
          showErrorNotification('Upload Failed', errorMsg, 'error');
          errors.push(errorMsg);
          continue;
        }
        // Convert file to base64
        const base64 = await new Promise<string>((resolve) => {
          const reader = new FileReader();
          reader.onload = () => resolve(reader.result as string);
          reader.readAsDataURL(file);
        });

        // Determine content type
        let contentType = 'file';
        const fileExtension = file.name.split('.').pop()?.toLowerCase() || '';

        if (file.type.startsWith('image/')) {
          contentType = 'image';
        } else if (file.type.startsWith('video/')) {
          contentType = 'video';
        } else if (file.type.startsWith('audio/')) {
          contentType = 'audio';
        } else if (
          file.type.includes('zip') ||
          file.type.includes('compressed') ||
          file.type.includes('archive') ||
          ['zip', 'rar', '7z', 'tar', 'gz', 'bz2', 'xz', 'zipx', 'cab', 'iso', 'dmg'].includes(fileExtension)
        ) {
          contentType = 'archive';
        } else if (
          file.type.startsWith('text/') ||
          ['txt', 'md', 'json', 'xml', 'csv', 'log', 'yaml', 'yml'].includes(fileExtension)
        ) {
          contentType = 'text';
        }

        // Create new block content using Firebase UID directly
        const newContent = {
          block_id: currentBlock.id,
          user_id: user.uid, // Use Firebase UID directly
          title: file.name,
          content: base64,
          content_type: contentType as 'text' | 'image' | 'video' | 'audio' | 'file' | 'archive',
          position: blockContents.length
        };

        console.log('Uploading file with content:', newContent);

        const { data, error } = await supabase
          .from('block_contents')
          .insert([newContent])
          .select()
          .single();

        if (error) {
          console.error('Error uploading file:', error);
          console.error('Error details:', {
            message: error.message,
            details: error.details,
            hint: error.hint,
            code: error.code
          });
          console.error('Full error object:', JSON.stringify(error, null, 2));

          // Show user-friendly error message
          showErrorNotification('Upload Failed', `${file.name}: ${error.message}`, 'error');
          errors.push(`${file.name}: Upload failed - ${error.message}`);
          continue;
        }

        // Update local state
        console.log('Before update - blockContents length:', blockContents.length);
        console.log('Before update - filteredBlockContents length:', filteredBlockContents.length);

        setBlockContents(prev => {
          const newArray = [...prev, data];
          console.log('Updated blockContents length:', newArray.length);
          return newArray;
        });

        setFilteredBlockContents(prev => {
          const newArray = [...prev, data];
          console.log('Updated filteredBlockContents length:', newArray.length);
          return newArray;
        });

        console.log('File uploaded successfully:', data);
        successCount++;

      } catch (error) {
        console.error('Error processing file:', file.name, error);
        const errorMsg = `Upload failed - ${error instanceof Error ? error.message : 'Unknown error'}`;
        showErrorNotification('Upload Failed', `${file.name}: ${errorMsg}`, 'error');
        errors.push(`${file.name}: ${errorMsg}`);
      }
    }

    // Show results to user
    if (successCount > 0 && errors.length === 0) {
      // Only show success message if there were no errors
      if (successCount === 1) {
        showErrorNotification('Upload Successful', '1 file uploaded successfully', 'success');
      } else {
        showErrorNotification('Upload Successful', `${successCount} files uploaded successfully`, 'success');
      }
    } else if (successCount > 0 && errors.length > 0) {
      // Mixed results
      showErrorNotification('Upload Completed', `${successCount} file(s) uploaded successfully, ${errors.length} failed`, 'warning');
    }
    // Individual errors are already shown above, no need for summary alert

    // Switch to contents tab to show the uploaded files
    if (successCount > 0) {
      setActiveTab('contents');
      console.log('Switched to contents tab after upload');
    }
  };

  // Handle file click - view file content
  const handleFileClick = (e: React.MouseEvent, content: BlockContent) => {
    e.preventDefault();
    e.stopPropagation();

    console.log('File clicked:', content.title, 'Type:', content.content_type);

    // Open file viewer modal
    setViewingFile(content);
  };

  // Handle subblock editing
  const handleEditSubBlock = (subBlock: VaultBlock) => {
    setEditingSubBlockId(subBlock.id);
    setEditingSubBlockName(subBlock.name);
    setOpenSubBlockMenuId(null);
  };

  const handleSaveSubBlockName = async (subBlockId: string) => {
    if (!editingSubBlockName.trim()) return;

    try {
      const { error } = await supabase
        .from('vault_blocks')
        .update({ name: editingSubBlockName.trim() })
        .eq('id', subBlockId);

      if (error) {
        console.error('Error updating subblock name:', error);
        return;
      }

      // Update local state
      setSubBlocks(prev => prev.map(block =>
        block.id === subBlockId
          ? { ...block, name: editingSubBlockName.trim() }
          : block
      ));

      setFilteredSubBlocks(prev => prev.map(block =>
        block.id === subBlockId
          ? { ...block, name: editingSubBlockName.trim() }
          : block
      ));

      setEditingSubBlockId(null);
      setEditingSubBlockName('');
    } catch (error) {
      console.error('Error updating subblock name:', error);
    }
  };

  const handleCancelEdit = () => {
    setEditingSubBlockId(null);
    setEditingSubBlockName('');
  };

  const handleDeleteSubBlock = async (subBlockId: string) => {
    if (!window.confirm('Are you sure you want to delete this folder? This action cannot be undone.')) {
      return;
    }

    try {
      const { error } = await supabase
        .from('vault_blocks')
        .delete()
        .eq('id', subBlockId);

      if (error) {
        console.error('Error deleting subblock:', error);
        return;
      }

      // Update local state
      setSubBlocks(prev => prev.filter(block => block.id !== subBlockId));
      setFilteredSubBlocks(prev => prev.filter(block => block.id !== subBlockId));
      setOpenSubBlockMenuId(null);

      // Update parent's total_items count
      if (currentBlock) {
        await supabase
          .from('vault_blocks')
          .update({ total_items: subBlocks.length - 1 })
          .eq('id', currentBlock.id);
      }
    } catch (error) {
      console.error('Error deleting subblock:', error);
    }
  };

  // Helper functions for file name handling
  const getFileNameWithoutExtension = (fileName: string): string => {
    const lastDotIndex = fileName.lastIndexOf('.');
    if (lastDotIndex === -1 || lastDotIndex === 0) {
      return fileName; // No extension or hidden file
    }
    return fileName.substring(0, lastDotIndex);
  };

  const getFileExtension = (fileName: string): string => {
    const lastDotIndex = fileName.lastIndexOf('.');
    if (lastDotIndex === -1 || lastDotIndex === 0) {
      return ''; // No extension or hidden file
    }
    return fileName.substring(lastDotIndex);
  };

  // File management functions
  const handleEditFile = (file: BlockContent) => {
    setEditingFileId(file.id);
    // Only edit the filename without extension
    setEditingFileName(getFileNameWithoutExtension(file.title));
    setOpenFileMenuId(null);
  };

  const handleSaveFileName = async (fileId: string) => {
    if (!editingFileName.trim()) return;

    // Find the original file to get its extension
    const originalFile = blockContents.find(content => content.id === fileId);
    if (!originalFile) return;

    const fileExtension = getFileExtension(originalFile.title);
    const newFileName = editingFileName.trim() + fileExtension;

    try {
      const { error } = await supabase
        .from('block_contents')
        .update({ title: newFileName })
        .eq('id', fileId);

      if (error) {
        console.error('Error updating file name:', error);
        return;
      }

      // Update local state
      setBlockContents(prev => prev.map(content =>
        content.id === fileId
          ? { ...content, title: newFileName }
          : content
      ));

      setFilteredBlockContents(prev => prev.map(content =>
        content.id === fileId
          ? { ...content, title: newFileName }
          : content
      ));

      setEditingFileId(null);
      setEditingFileName('');
    } catch (error) {
      console.error('Error updating file name:', error);
    }
  };

  const handleDeleteFile = async (fileId: string) => {
    if (!confirm('Are you sure you want to delete this file? This action cannot be undone.')) {
      return;
    }

    try {
      const { error } = await supabase
        .from('block_contents')
        .delete()
        .eq('id', fileId);

      if (error) {
        console.error('Error deleting file:', error);
        return;
      }

      // Update local state
      setBlockContents(prev => prev.filter(content => content.id !== fileId));
      setFilteredBlockContents(prev => prev.filter(content => content.id !== fileId));
      setOpenFileMenuId(null);
    } catch (error) {
      console.error('Error deleting file:', error);
    }
  };

  // Tab state - default to drafts
  const [activeTab, setActiveTab] = useState<'contents' | 'drafts'>('drafts');
  const [newBlockName, setNewBlockName] = useState("");
  const [newContentTitle, setNewContentTitle] = useState("");
  const [newContentText, setNewContentText] = useState("");

  // User settings for vault name and banner
  const [vaultName, setVaultName] = useState("My Vault");
  const [bannerImage, setBannerImage] = useState<string | null>(null);
  const [userSettings, setUserSettings] = useState<any>(null);

  const userDropdownRef = useRef<HTMLDivElement>(null);
  const createDropdownRef = useRef<HTMLDivElement>(null);
  const notificationDropdownRef = useRef<HTMLDivElement>(null);

  // Load block data
  useEffect(() => {
    if (user && blockId) {
      loadBlockData();
      loadUserSettings();
    }
  }, [user, blockId]);

  // Initialize filtered arrays when data loads
  useEffect(() => {
    setFilteredSubBlocks(subBlocks);
    setFilteredBlockContents(blockContents);
  }, [subBlocks, blockContents, draftNotes]);

  // Handle click outside for dropdowns
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;

      if (createDropdownRef.current && !createDropdownRef.current.contains(event.target as Node)) {
        setShowCreateDropdown(false);
      }

      // Close ellipsis menus only if clicking outside of them
      if (openSubBlockMenuId && !target.closest('.ellipsis-menu') && !target.closest('.ellipsis-button')) {
        setOpenSubBlockMenuId(null);
      }
      if (openFileMenuId && !target.closest('.ellipsis-menu') && !target.closest('.ellipsis-button')) {
        setOpenFileMenuId(null);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [openSubBlockMenuId, openFileMenuId]);

  // Load user settings from users table
  const loadUserSettings = async () => {
    if (!user) return;

    try {
      const { data, error } = await supabase
        .from('users')
        .select('*')
        .eq('id', user.uid)
        .single();

      if (error) {
        console.error('Error loading user data:', error);
        return;
      }

      if (data) {
        setUserSettings(data);
        setVaultName(data.vault_name || data.display_name || 'My Vault');
        setBannerImage(data.vault_banner_image || null);
      }
    } catch (error) {
      console.error('Error loading user settings:', error);
    }
  };

  // Set up real-time subscription for user settings
  useEffect(() => {
    if (!user) return;

    const userSettingsSubscription = supabase
      .channel(`users_changes_block_${user.uid}`)
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'users',
          filter: `id=eq.${user.uid}`
        },
        (payload) => {
          console.log('User data changed in BlockContent:', payload);
          if (payload.new) {
            setUserSettings(payload.new);
            setVaultName(payload.new.vault_name || payload.new.display_name || 'My Vault');
            setBannerImage(payload.new.vault_banner_image || null);
          }
        }
      )
      .subscribe();

    // Listen for manual vault settings updates
    const handleVaultSettingsUpdate = (event: CustomEvent) => {
      console.log('Manual vault settings update received in BlockContent:', event.detail);
      if (event.detail.vault_name !== undefined) {
        setVaultName(event.detail.vault_name || 'My Vault');
      }
      if (event.detail.vault_banner_image !== undefined) {
        setBannerImage(event.detail.vault_banner_image || null);
      }
    };

    window.addEventListener('vaultSettingsUpdated', handleVaultSettingsUpdate as EventListener);

    return () => {
      userSettingsSubscription.unsubscribe();
      window.removeEventListener('vaultSettingsUpdated', handleVaultSettingsUpdate as EventListener);
    };
  }, [user]);

  const loadBlockData = async () => {
    if (!user || !blockId) return;

    try {
      setLoading(true);

      // Load current block
      const { data: blockData, error: blockError } = await supabase
        .from('vault_blocks')
        .select('*')
        .eq('id', blockId)
        .eq('user_id', user.uid)
        .single();

      if (blockError) {
        console.error('Error loading block:', blockError);
        navigate('/my-vault');
        return;
      }

      setCurrentBlock(blockData);

      // Build breadcrumb
      await buildBreadcrumb(blockData);

      // Load sub-blocks
      const { data: subBlocksData, error: subBlocksError } = await supabase
        .from('vault_blocks')
        .select('*')
        .eq('parent_id', blockId)
        .eq('user_id', user.uid)
        .order('position', { ascending: true });

      if (subBlocksError) {
        console.error('Error loading sub-blocks:', subBlocksError);
      } else {
        setSubBlocks(subBlocksData || []);
      }

      // Load block contents (published and drafts)
      const { data: contentsData, error: contentsError } = await supabase
        .from('block_contents')
        .select('*')
        .eq('block_id', blockId)
        .order('created_at', { ascending: false });

      if (contentsError) {
        console.error('Error loading block contents:', contentsError);
        setBlockContents([]);
        setDraftNotes([]);
      } else {
        const allContents = contentsData || [];
        // Separate drafts from published content
        // For now, consider content with empty or minimal content as drafts
        const drafts = allContents.filter(content =>
          content.content === '' ||
          content.title === 'Untitled Draft' ||
          (content.content && content.content.length < 50 && content.content_type === 'text')
        );
        const published = allContents.filter(content =>
          content.content !== '' &&
          content.title !== 'Untitled Draft' &&
          !(content.content && content.content.length < 50 && content.content_type === 'text')
        );

        setBlockContents(published);
        setDraftNotes(drafts);
      }

    } catch (error) {
      console.error('Error loading block data:', error);
    } finally {
      setLoading(false);
    }
  };

  const buildBreadcrumb = async (block: VaultBlock) => {
    const breadcrumbPath: VaultBlock[] = [];
    let currentBlock = block;

    // Build path from current block to root
    while (currentBlock.parent_id) {
      const { data: parentData, error } = await supabase
        .from('vault_blocks')
        .select('*')
        .eq('id', currentBlock.parent_id)
        .eq('user_id', user!.uid)
        .single();

      if (error || !parentData) break;
      
      breadcrumbPath.unshift(parentData);
      currentBlock = parentData;
    }

    setBreadcrumb(breadcrumbPath);
  };

  const createSubBlock = async () => {
    if (!user || !currentBlock || !newBlockName.trim()) return;

    try {
      const newBlockData = {
        user_id: user.uid,
        parent_id: currentBlock.id,
        name: newBlockName.trim(),
        smart_tag: currentBlock.smart_tag,
        banner_gradient: currentBlock.banner_gradient,
        is_pinned: false,
        total_items: 0,
        position: subBlocks.length,
        depth: currentBlock.depth + 1
      };

      const { data, error } = await supabase
        .from('vault_blocks')
        .insert([newBlockData])
        .select()
        .single();

      if (error) {
        console.error('Error creating sub-block:', error);
        return;
      }

      setSubBlocks([...subBlocks, data]);
      setNewBlockName("");
      setShowAddBlockModal(false);

      // Update parent's total_items count
      await supabase
        .from('vault_blocks')
        .update({ total_items: subBlocks.length + 1 })
        .eq('id', currentBlock.id);

    } catch (error) {
      console.error('Error creating sub-block:', error);
    }
  };

  const createFolder = async (folderData: { name: string; color: string }) => {
    if (!user || !currentBlock) return;

    try {
      const newFolderData = {
        user_id: user.uid,
        parent_id: currentBlock.id,
        name: folderData.name,
        smart_tag: 'Folder',
        banner_gradient: folderData.color,
        is_pinned: false,
        total_items: 0,
        position: subBlocks.length,
        depth: currentBlock.depth + 1
      };

      const { data, error } = await supabase
        .from('vault_blocks')
        .insert([newFolderData])
        .select()
        .single();

      if (error) {
        console.error('Error creating folder:', error);
        throw error;
      }

      setSubBlocks([...subBlocks, data]);

      // Update parent's total_items count
      await supabase
        .from('vault_blocks')
        .update({ total_items: subBlocks.length + 1 })
        .eq('id', currentBlock.id);

    } catch (error) {
      console.error('Error creating folder:', error);
      throw error;
    }
  };

  // Handle search changes from VaultSearch component
  const handleSearchChange = (query: string, filter: FilterType, tag: string) => {
    setSearchQuery(query);
    setSearchFilter(filter);
    setSearchTag(tag);
  };

  // Handle sort changes from VaultSearch component
  const handleSortChange = (newSortBy: 'name' | 'date' | 'tag' | 'items', newSortOrder: 'asc' | 'desc') => {
    // Only accept 'name' and 'date' for subblock section
    if (newSortBy === 'name' || newSortBy === 'date') {
      setSortBy(newSortBy);
      setSortOrder(newSortOrder);
    }
  };

  const handleNavigation = (path: string) => {
    navigate(path);
  };

  // Create a new draft note
  const createDraftNote = async () => {
    if (!user || !blockId) return;

    // Switch to drafts tab and open editor for new note
    setActiveTab('drafts');
    setEditingDraft(null); // null means new draft
    setIsEditorOpen(true);
  };

  // Open editor for existing draft
  const openDraftEditor = (draft: BlockContent) => {
    setEditingDraft(draft);
    setIsEditorOpen(true);
  };

  // Close editor drawer
  const closeEditor = () => {
    setIsEditorOpen(false);
    setEditingDraft(null);
  };

  // Handle draft save from editor
  const handleDraftSave = (savedDraft: BlockContent) => {
    setDraftNotes(prev => {
      const existingIndex = prev.findIndex(d => d.id === savedDraft.id);
      if (existingIndex >= 0) {
        // Update existing draft
        const updated = [...prev];
        updated[existingIndex] = savedDraft;
        return updated;
      } else {
        // Add new draft
        return [savedDraft, ...prev];
      }
    });
    setEditingDraft(savedDraft);
  };

  // Handle draft deletion from editor
  const handleDraftDelete = (draftId: string) => {
    setDraftNotes(prev => prev.filter(d => d.id !== draftId));
  };

  // Filter sub-blocks and content based on search criteria
  useEffect(() => {
    let filteredBlocks: VaultBlock[] = [];
    let filteredContents: BlockContent[] = [];

    // Apply filter type first
    const shouldIncludeBlocks = searchFilter === 'all' || searchFilter === 'blocks' || searchFilter === 'folders';
    const shouldIncludeContent = searchFilter === 'all' || searchFilter === 'content' || searchFilter === 'files';

    if (searchQuery.trim()) {
      // Use fuzzy search for blocks
      if (shouldIncludeBlocks) {
        const blockFuse = new Fuse(subBlocks, {
          keys: [
            { name: 'name', weight: 0.7 },
            { name: 'smart_tag', weight: 0.3 }
          ],
          threshold: 0.4,
          includeScore: true,
          minMatchCharLength: 2
        });

        const blockResults = blockFuse.search(searchQuery);
        filteredBlocks = blockResults.map(result => result.item);
      }

      // Use fuzzy search for content
      if (shouldIncludeContent) {
        const contentFuse = new Fuse(blockContents, {
          keys: [
            { name: 'title', weight: 0.6 },
            { name: 'content', weight: 0.4 }
          ],
          threshold: 0.5,
          includeScore: true,
          minMatchCharLength: 2
        });

        const contentResults = contentFuse.search(searchQuery);
        filteredContents = contentResults.map(result => result.item);
      }
    } else {
      // No query - apply filter only
      filteredBlocks = shouldIncludeBlocks ? subBlocks : [];
      filteredContents = shouldIncludeContent ? blockContents : [];
    }

    setFilteredSubBlocks(filteredBlocks);
    setFilteredBlockContents(filteredContents);

    // Create combined content array (folders first, then files)
    const combinedContent: (VaultBlock | BlockContent)[] = [
      ...filteredBlocks,
      ...filteredContents
    ];
    setFilteredCombinedContent(combinedContent);
  }, [searchQuery, searchFilter, searchTag, subBlocks, blockContents]);

  // Sort filtered items
  const sortedSubBlocks = [...filteredSubBlocks].sort((a, b) => {
    let comparison = 0;

    switch (sortBy) {
      case 'name':
        comparison = a.name.localeCompare(b.name);
        break;
      case 'date':
        comparison = new Date(a.created_at).getTime() - new Date(b.created_at).getTime();
        break;
      default:
        comparison = 0;
    }

    return sortOrder === 'asc' ? comparison : -comparison;
  });

  const sortedBlockContents = [...filteredBlockContents].sort((a, b) => {
    let comparison = 0;

    switch (sortBy) {
      case 'name':
        comparison = a.title.localeCompare(b.title);
        break;
      case 'date':
        comparison = new Date(a.created_at).getTime() - new Date(b.created_at).getTime();
        break;
      default:
        comparison = 0;
    }

    return sortOrder === 'asc' ? comparison : -comparison;
  });

  // Helper function to check if item is a folder
  const isFolder = (item: VaultBlock | BlockContent): item is VaultBlock => {
    return 'smart_tag' in item;
  };

  // Sort combined content (folders first, then files)
  const sortedCombinedContent = [...filteredCombinedContent].sort((a, b) => {
    // First, separate folders and files
    const aIsFolder = isFolder(a);
    const bIsFolder = isFolder(b);

    // Folders always come first
    if (aIsFolder && !bIsFolder) return -1;
    if (!aIsFolder && bIsFolder) return 1;

    // If both are same type, sort by the selected criteria
    let comparison = 0;

    switch (sortBy) {
      case 'name':
        const aName = aIsFolder ? a.name : a.title;
        const bName = bIsFolder ? b.name : b.title;
        comparison = aName.localeCompare(bName);
        break;
      case 'date':
        comparison = new Date(a.created_at).getTime() - new Date(b.created_at).getTime();
        break;
      default:
        comparison = 0;
    }

    return sortOrder === 'asc' ? comparison : -comparison;
  });

  // Debug logging
  useEffect(() => {
    console.log('=== CONTENT ARRAYS DEBUG ===');
    console.log('Block contents:', blockContents.length, blockContents);
    console.log('Filtered block contents:', filteredBlockContents.length, filteredBlockContents);
    console.log('Sorted block contents:', sortedBlockContents.length, sortedBlockContents);
    console.log('Current tab:', activeTab);
    console.log('============================');
  }, [blockContents, filteredBlockContents, sortedBlockContents]);

  const handleLogout = async () => {
    setLogoutLoading(true);
    try {
      await logout();
      navigate("/login");
    } catch (error) {
      console.error("Logout error:", error);
    } finally {
      setLogoutLoading(false);
    }
  };

  const sidebarItems = [
    { icon: HomeIcon, label: "home", active: false, path: "/home" },
    { icon: Archive, label: vaultName.toLowerCase(), active: true, path: "/my-vault" },
    { icon: Brain, label: "mind games", active: false, path: "/mind-games" },
    { icon: Map, label: "visual maps", active: false, path: "/visual-maps" },
    { icon: Trophy, label: "achievements", active: false, path: "/achievements" },
  ];

  if (loading) {
    return <LoadingScreen message="Loading block content..." />;
  }

  if (!currentBlock) {
    return <LoadingScreen message="Block not found..." />;
  }

  return (
    <>
      {logoutLoading && <LoadingScreen message="signing you out..." />}
      <div
        className="min-h-screen w-full font-sans relative"
        style={{
          backgroundImage: 'url(/background.jpg)',
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundRepeat: 'no-repeat',
          backgroundAttachment: 'fixed'
        }}
      >
        {/* Subtle overlay to tone down the background */}
        <div className="absolute inset-0 bg-[#2E0406]/40 backdrop-blur-[0.5px]" />

        {/* Top Navigation Bar */}
        <div className="w-full px-3 py-6 relative z-10">
          <div className="bg-[#faf7f2]/90 backdrop-blur-md border border-[#e0d7cc]/60 rounded-2xl shadow-lg p-4 relative z-50 max-w-[90%] mx-auto">
            <div className="flex items-center justify-between">
              {/* Left: Hamburger/Close and Logo */}
              <div className="flex items-center gap-4">
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => setSidebarOpen(!sidebarOpen)}
                  className="w-9 h-9 rounded-xl hover:bg-[#d4c7b8]/40 transition-all duration-200"
                >
                  {sidebarOpen ? (
                    <X className="w-4 h-4 text-[#5a4a3a]/80" />
                  ) : (
                    <Menu className="w-4 h-4 text-[#5a4a3a]/80" />
                  )}
                </Button>
                <div className="flex items-center gap-3">
                  <img
                    src="/app logo.png"
                    alt="Logo"
                    className="w-9 h-9 rounded-xl object-cover shadow-sm"
                  />
                </div>
              </div>

              {/* Center: Empty space for cleaner navbar */}
              <div className="flex-1"></div>

              {/* Right: User controls */}
              <div className="flex items-center gap-3">
                {/* User */}
                <div className="relative flex items-center gap-2" ref={userDropdownRef}>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="w-10 h-10 rounded-full hover:bg-[#d4c7b8]/40 transition-all duration-200 p-0"
                  >
                    {user?.photoURL ? (
                      <img
                        src={user.photoURL}
                        alt="Profile"
                        className="w-8 h-8 rounded-full object-cover"
                      />
                    ) : (
                      <User className="w-5 h-5 text-[#5a4a3a]/80" />
                    )}
                  </Button>
                  <span className="text-sm font-poppins text-[#2E0406]/90">
                    {user?.displayName || 'user'}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content Area */}
        <div className="flex px-3 pb-6 h-[calc(100vh-144px)] max-w-[90%] mx-auto relative z-10">
          {/* Sidebar */}
          <div className={`transition-all duration-500 ease-in-out ${
            sidebarOpen ? 'w-52 mr-5' : 'w-16 mr-4'
          }`}>
            <div className="bg-[#faf7f2]/90 backdrop-blur-md border border-[#e0d7cc]/60 rounded-2xl shadow-lg h-full relative overflow-hidden">
              {/* Full Sidebar Content */}
              <div className={`absolute inset-0 p-4 transition-all duration-500 ease-in-out ${
                sidebarOpen ? 'opacity-100 translate-x-0' : 'opacity-0 translate-x-[-100%]'
              }`}>
                {/* Navigation Items */}
                <div className="space-y-2 mb-8">
                  {sidebarItems.map((item, index) => (
                    <Button
                      key={index}
                      variant="ghost"
                      onClick={() => handleNavigation(item.path)}
                      className={`w-full justify-start gap-3 h-12 rounded-xl font-poppins text-sm transition-all duration-200 relative ${
                        item.active
                          ? 'bg-[#2E0406]/80 text-[#faf7f2] hover:bg-[#3d1a1c]/80 backdrop-blur-sm border border-[#5a4a3a]/20 shadow-lg'
                          : 'text-[#5a4a3a] hover:bg-[#e0d7cc]/40 hover:text-[#2E0406]'
                      }`}
                    >
                      <item.icon className="w-4 h-4" />
                      <span>{item.label}</span>
                    </Button>
                  ))}
                </div>

                {/* Logout Button */}
                <div className="absolute bottom-4 left-4 right-4">
                  <Button
                    variant="ghost"
                    onClick={handleLogout}
                    className="w-full justify-start gap-3 h-12 rounded-xl font-poppins text-sm text-[#8b2635] hover:bg-[#8b2635]/10 hover:text-[#6d1f2c] transition-all duration-200"
                  >
                    <LogOut className="w-4 h-4" />
                    <span>Logout</span>
                  </Button>
                </div>
              </div>

              {/* Icon-Only Sidebar Content */}
              <div className={`absolute inset-0 p-3 transition-all duration-500 ease-in-out ${
                sidebarOpen ? 'opacity-0 translate-x-[100%]' : 'opacity-100 translate-x-0'
              }`}>
                {/* Navigation Icons */}
                <div className="space-y-2 mb-8">
                  {sidebarItems.map((item, index) => (
                    <Button
                      key={index}
                      variant="ghost"
                      size="icon"
                      onClick={() => handleNavigation(item.path)}
                      className={`w-10 h-10 rounded-xl transition-all duration-200 ${
                        item.active
                          ? 'bg-[#2E0406]/80 text-[#faf7f2] hover:bg-[#3d1a1c]/80'
                          : 'text-[#5a4a3a] hover:bg-[#e0d7cc]/40 hover:text-[#2E0406]'
                      }`}
                      title={item.label}
                    >
                      <item.icon className="w-4 h-4" />
                    </Button>
                  ))}
                </div>

                {/* Logout Icon */}
                <div className="absolute bottom-3 left-3 right-3">
                  <Button
                    onClick={handleLogout}
                    variant="ghost"
                    size="icon"
                    className="w-10 h-10 rounded-xl text-[#8b2635] hover:bg-[#8b2635]/10 transition-all duration-200"
                    title="logout"
                  >
                    <LogOut className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </div>
          </div>

          {/* Main Content */}
          <div className="flex-1 transition-all duration-500 ease-in-out">
            <div className="bg-gradient-to-br from-[#f8f4ee] via-[#faf7f2] to-[#f5f0e8] h-full overflow-hidden rounded-2xl">
              {/* Scrollable Content Container */}
              <div className="h-full overflow-y-auto scrollbar-thin scrollbar-thumb-[#8b2635] scrollbar-track-transparent hover:scrollbar-thumb-[#6d1f2c]">
                <div className="p-8">
                  {/* Block Banner Section */}
                  <div className="relative h-32 overflow-hidden rounded-3xl mb-6 shadow-2xl">
                    {currentBlock.custom_banner_image ? (
                      <img
                        src={currentBlock.custom_banner_image}
                        alt="Block Banner"
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <div className={`w-full h-full bg-gradient-to-r ${currentBlock.banner_gradient}`} />
                    )}

                    {/* Banner Overlay */}
                    <div className="absolute inset-0 bg-black/20" />
                  </div>

                  {/* Block Title Below Banner */}
                  <div className="mb-6">
                    <div className="flex items-start justify-between mb-3">
                      <h1 className="text-4xl md:text-5xl font-cormorant font-bold text-[#2E0406]">
                        {currentBlock.name}
                      </h1>

                      {/* Create Dropdown */}
                      <div className="relative" ref={createDropdownRef}>
                        <Button
                          onClick={() => setShowCreateDropdown(!showCreateDropdown)}
                          className="bg-gradient-to-r from-[#8b2635] to-[#6d1f2c] hover:from-[#6d1f2c] hover:to-[#8b2635] text-white border-0 rounded-xl h-12 px-6 font-poppins shadow-lg hover:shadow-xl transition-all duration-200"
                        >
                          <Plus className="w-5 h-5 mr-2" />
                          Create
                          <ChevronDown className="w-4 h-4 ml-2" />
                        </Button>

                        {/* Dropdown Menu */}
                        {showCreateDropdown && (
                          <div className="absolute right-0 top-full mt-2 w-48 bg-white rounded-xl shadow-lg border border-[#e0d7cc]/40 py-2 z-50">
                            <button
                              onClick={() => {
                                setShowAddFolderModal(true);
                                setShowCreateDropdown(false);
                              }}
                              className="w-full px-4 py-3 text-left hover:bg-[#f5f0e8] transition-colors flex items-center gap-3 font-poppins text-[#2E0406]"
                            >
                              <FolderPlus className="w-4 h-4 text-[#8b2635]" />
                              New Folder
                            </button>
                            <button
                              onClick={() => {
                                createDraftNote();
                                setShowCreateDropdown(false);
                              }}
                              className="w-full px-4 py-3 text-left hover:bg-[#f5f0e8] transition-colors flex items-center gap-3 font-poppins text-[#2E0406]"
                            >
                              <FileText className="w-4 h-4 text-[#8b2635]" />
                              New Note
                            </button>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Block Metadata */}
                    <div className="flex items-center gap-4 text-sm text-[#8b7355] mb-3">
                      <span className="bg-[#8b2635]/10 text-[#8b2635] px-3 py-1 rounded-full font-poppins">
                        {currentBlock.smart_tag}
                      </span>
                      <span className="font-poppins">
                        {subBlocks.length} folders
                      </span>
                      <span className="font-poppins">
                        {blockContents.length} items
                      </span>
                    </div>

                    {/* Directory Path */}
                    <div className="flex items-center gap-2 text-sm">
                      <span className="text-[#8b7355] font-poppins font-medium">Directory:</span>
                      <button
                        onClick={() => navigate('/my-vault')}
                        className="text-[#8b7355] hover:text-[#2E0406] transition-colors font-poppins"
                      >
                        {vaultName}
                      </button>
                      {breadcrumb.map((block, index) => (
                        <React.Fragment key={block.id}>
                          <ChevronRight className="w-4 h-4 text-[#8b7355]" />
                          <button
                            onClick={() => navigate(`/block/${block.id}`)}
                            className="text-[#8b7355] hover:text-[#2E0406] transition-colors font-poppins"
                          >
                            {block.name}
                          </button>
                        </React.Fragment>
                      ))}
                      <ChevronRight className="w-4 h-4 text-[#8b7355]" />
                      <span className="text-[#2E0406] font-poppins font-medium">
                        {currentBlock.name}
                      </span>
                    </div>
                  </div>

                  {/* Tabbed Interface */}
                  <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as 'contents' | 'drafts')} className="w-full">
                    <TabsList className="grid w-full grid-cols-2 mb-6 bg-[#f5f0e8] p-1 rounded-xl h-14 border border-[#e0d7cc]/40">
                      <TabsTrigger
                        value="contents"
                        className="bg-transparent text-[#8b7355] data-[state=active]:bg-[#8b2635] data-[state=active]:text-white data-[state=active]:shadow-none transition-all duration-300 ease-in-out rounded-lg font-poppins h-full py-3 text-sm font-medium m-0 border-0 flex items-center gap-2"
                      >
                        <Archive className="w-4 h-4" />
                        Contents ({subBlocks.length + blockContents.length})
                      </TabsTrigger>
                      <TabsTrigger
                        value="drafts"
                        className="bg-transparent text-[#8b7355] data-[state=active]:bg-[#8b2635] data-[state=active]:text-white data-[state=active]:shadow-none transition-all duration-300 ease-in-out rounded-lg font-poppins h-full py-3 text-sm font-medium m-0 border-0 flex items-center gap-2"
                      >
                        <Edit3 className="w-4 h-4" />
                        Drafts ({draftNotes.length})
                      </TabsTrigger>
                    </TabsList>

                    {/* Contents Tab Content */}
                    <TabsContent value="contents" className="mt-0">
                      {/* Search Section */}
                      <div className="mb-6">
                        <VaultSearch
                          onSearchChange={handleSearchChange}
                          onSortChange={handleSortChange}
                        />
                      </div>

                      {/* File Drop Zone */}
                      <div
                        onDragOver={handleDragOver}
                        onDragLeave={handleDragLeave}
                        onDrop={handleDrop}
                        className={`relative ${isDragOver ? 'bg-blue-50 border-blue-300' : ''}`}
                      >
                        {/* Drag Overlay */}
                        {isDragOver && (
                          <div className="absolute inset-0 bg-blue-100/80 border-2 border-dashed border-blue-400 rounded-xl flex items-center justify-center z-10">
                            <div className="text-center">
                              <div className="w-16 h-16 bg-blue-500 rounded-full flex items-center justify-center mx-auto mb-4">
                                <FileText className="w-8 h-8 text-white" />
                              </div>
                              <p className="text-blue-700 font-poppins font-semibold text-lg">Drop files here</p>
                              <p className="text-blue-600 font-poppins text-sm">Files will be uploaded to this section</p>
                            </div>
                          </div>
                        )}

                      {/* Combined Content Grid */}
                      {sortedCombinedContent.length > 0 ? (
                        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
                          {sortedCombinedContent.map((item) => {
                            const itemIsFolder = isFolder(item);

                            if (itemIsFolder) {
                              // Render folder
                              return (
                                <div
                                  key={item.id}
                                  onClick={() => navigate(`/block/${item.id}`)}
                                  className="group bg-white/60 backdrop-blur-sm border border-[#e0d7cc]/40 rounded-2xl hover:shadow-lg hover:scale-105 transition-all duration-300 cursor-pointer relative p-4"
                                >
                                  <div className="flex justify-center mb-3">
                                    <div className="w-12 h-12 flex items-center justify-center">
                                      <Folder className="w-8 h-8 text-yellow-500" />
                                    </div>
                                  </div>
                                  <div className="text-center">
                                    {/* Folder Name and Menu */}
                                    <div className="flex items-center justify-center mb-2 relative w-full">
                                      {editingSubBlockId === item.id ? (
                                        <input
                                          type="text"
                                          value={editingSubBlockName}
                                          onChange={(e) => setEditingSubBlockName(e.target.value)}
                                          onBlur={() => handleSaveSubBlockName(item.id)}
                                          onKeyDown={(e) => {
                                            if (e.key === 'Enter') {
                                              handleSaveSubBlockName(item.id);
                                            } else if (e.key === 'Escape') {
                                              setEditingSubBlockId(null);
                                              setEditingSubBlockName('');
                                            }
                                          }}
                                          className="bg-white/80 border border-[#8b2635]/30 rounded-lg px-2 py-1 text-sm font-cormorant font-bold text-[#2E0406] text-center focus:outline-none focus:ring-2 focus:ring-[#8b2635]/30"
                                          autoFocus
                                        />
                                      ) : (
                                        <h3 className="font-cormorant font-bold text-[#2E0406] text-sm group-hover:text-[#8b2635] transition-colors text-center px-1 truncate">
                                          {item.name}
                                        </h3>
                                      )}

                                      {/* Ellipsis Menu */}
                                      <div className="absolute top-0 right-0">
                                        <button
                                          onClick={(e) => {
                                            e.stopPropagation();
                                            setOpenSubBlockMenuId(openSubBlockMenuId === item.id ? null : item.id);
                                          }}
                                          className="ellipsis-button w-6 h-6 rounded-lg hover:bg-[#8b2635]/10 flex items-center justify-center transition-all duration-200 opacity-30 group-hover:opacity-100"
                                        >
                                          <MoreVertical className="w-4 h-4 text-[#8b2635]" />
                                        </button>

                                        {/* Dropdown Menu */}
                                        {openSubBlockMenuId === item.id && (
                                          <div
                                            className="ellipsis-menu absolute right-0 top-8 w-44 bg-white/90 backdrop-blur-md border border-white/30 rounded-xl shadow-2xl p-2 z-[9999]"
                                            onClick={(e) => e.stopPropagation()}
                                          >
                                            <button
                                              onClick={() => handleEditSubBlock(item)}
                                              className="w-full flex items-center gap-3 px-3 py-2 text-sm text-[#2E0406] hover:bg-[#8b2635]/10 rounded-lg transition-all duration-200"
                                            >
                                              <Edit3 className="w-4 h-4 text-[#8b2635]" />
                                              Edit Name
                                            </button>
                                            <button
                                              onClick={() => handleDeleteSubBlock(item.id)}
                                              className="w-full flex items-center gap-3 px-3 py-2 text-sm text-red-600 hover:bg-red-50 rounded-lg transition-all duration-200"
                                            >
                                              <Trash2 className="w-4 h-4 text-red-600" />
                                              Delete Folder
                                            </button>
                                          </div>
                                        )}
                                      </div>
                                    </div>

                                    <div className="text-xs text-[#6d1f2c] font-poppins">
                                      {formatDate(item.created_at)}
                                    </div>
                                  </div>
                                </div>
                              );
                            } else {
                              // Render file
                              const fileTypeInfo = getFileTypeIcon(item);
                              const IconComponent = fileTypeInfo.icon;

                              return (
                                <div
                                  key={item.id}
                                  className="group bg-white/60 backdrop-blur-sm border border-[#e0d7cc]/40 rounded-2xl hover:shadow-lg hover:scale-105 transition-all duration-300 cursor-pointer relative p-4"
                                >
                                  {/* File Icon */}
                                  <div
                                    className="flex justify-center mb-4 cursor-pointer hover:scale-110 transition-transform duration-200"
                                    onClick={(e) => handleFileClick(e, item)}
                                    title="Click to view file"
                                  >
                                    <div className={`w-16 h-16 ${fileTypeInfo.bgColor} rounded-xl flex items-center justify-center hover:shadow-lg transition-shadow duration-200`}>
                                      <IconComponent className={`w-8 h-8 ${fileTypeInfo.color}`} />
                                    </div>
                                  </div>

                                  {/* File Content */}
                                  <div className="text-center">
                                    {/* File Name and Menu */}
                                    <div className="flex items-center justify-center mb-2 relative w-full">
                                      {editingFileId === item.id ? (
                                        <div className="flex items-center justify-center">
                                          <input
                                            type="text"
                                            value={editingFileName}
                                            onChange={(e) => setEditingFileName(e.target.value)}
                                            onBlur={() => handleSaveFileName(item.id)}
                                            onKeyDown={(e) => {
                                              if (e.key === 'Enter') {
                                                handleSaveFileName(item.id);
                                              } else if (e.key === 'Escape') {
                                                setEditingFileId(null);
                                                setEditingFileName('');
                                              }
                                            }}
                                            className="bg-white/80 border border-[#8b2635]/30 rounded-lg px-2 py-1 text-sm font-cormorant font-bold text-[#2E0406] text-center focus:outline-none focus:ring-2 focus:ring-[#8b2635]/30"
                                            autoFocus
                                          />
                                          <span className="text-sm font-cormorant font-bold text-[#8b7355] ml-1">
                                            {getFileExtension(item.title)}
                                          </span>
                                        </div>
                                      ) : (
                                        <h3
                                          className="font-cormorant font-bold text-[#2E0406] text-lg group-hover:text-[#8b2635] transition-colors text-center px-1 truncate cursor-pointer hover:underline"
                                          onClick={(e) => handleFileClick(e, item)}
                                          title="Click to view file"
                                        >
                                          {item.title || 'Untitled'}
                                        </h3>
                                      )}

                                      {/* Ellipsis Menu */}
                                      <div className="absolute top-0 right-0">
                                        <button
                                          onClick={(e) => {
                                            e.stopPropagation();
                                            setOpenFileMenuId(openFileMenuId === item.id ? null : item.id);
                                          }}
                                          className="ellipsis-button w-6 h-6 rounded-lg hover:bg-[#8b2635]/10 flex items-center justify-center transition-all duration-200 opacity-30 group-hover:opacity-100"
                                        >
                                          <MoreVertical className="w-4 h-4 text-[#8b2635]" />
                                        </button>

                                        {/* Dropdown Menu */}
                                        {openFileMenuId === item.id && (
                                          <div
                                            className="ellipsis-menu absolute right-0 top-8 w-44 bg-white/90 backdrop-blur-md border border-white/30 rounded-xl shadow-2xl p-2 z-[9999]"
                                            onClick={(e) => e.stopPropagation()}
                                          >
                                            <button
                                              onClick={() => handleEditFile(item)}
                                              className="w-full flex items-center gap-3 px-3 py-2 text-sm text-[#2E0406] hover:bg-[#8b2635]/10 rounded-lg transition-all duration-200"
                                            >
                                              <Edit3 className="w-4 h-4 text-[#8b2635]" />
                                              Edit Name
                                            </button>
                                            <button
                                              onClick={() => handleDeleteFile(item.id)}
                                              className="w-full flex items-center gap-3 px-3 py-2 text-sm text-red-600 hover:bg-red-50 rounded-lg transition-all duration-200"
                                            >
                                              <Trash2 className="w-4 h-4 text-red-600" />
                                              Delete File
                                            </button>
                                          </div>
                                        )}
                                      </div>
                                    </div>

                                    {/* File Metadata */}
                                    <div className="text-xs text-[#8b7355] font-poppins space-y-1">
                                      <p>Type: {item.content_type}</p>
                                      <p>Size: {Math.round(item.content.length / 1024)} KB</p>
                                      <p>{formatDate(item.created_at)}</p>
                                    </div>
                                  </div>
                                </div>
                              );
                            }
                          })}
                        </div>
                      ) : (
                        <div className="text-center py-16">
                          <div className="w-24 h-24 bg-[#e0d7cc]/40 rounded-3xl flex items-center justify-center mx-auto mb-6">
                            <Archive className="w-12 h-12 text-[#8b7355]" />
                          </div>
                          <h3 className="font-cormorant font-bold text-[#2E0406] text-2xl mb-3">No content yet</h3>
                          <p className="text-[#8b7355] font-poppins mb-6 max-w-md mx-auto">
                            Create folders and upload files to organize your knowledge.
                          </p>
                        </div>
                      )}
                      </div>
                    </TabsContent>



                    {/* Drafts Tab Content */}
                    <TabsContent value="drafts" className="mt-0">
                      {/* Drafts Grid */}
                      {draftNotes.length > 0 ? (
                        <div className="space-y-4">
                          {draftNotes.map((draft) => (
                            <div
                              key={draft.id}
                              className="bg-white/40 border border-dashed border-[#e0d7cc] rounded-xl p-4 hover:bg-white/60 transition-colors cursor-pointer"
                              onClick={() => openDraftEditor(draft)}
                            >
                              <div className="flex items-start justify-between mb-2">
                                <h4 className="font-cormorant font-semibold text-[#2E0406] text-lg">
                                  {draft.title || 'Untitled Draft'}
                                </h4>
                                <div className="flex items-center gap-2">
                                  <span className="text-xs bg-orange-100 text-orange-600 px-2 py-1 rounded-full font-poppins">
                                    Draft
                                  </span>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      openDraftEditor(draft);
                                    }}
                                    className="h-8 w-8 p-0 hover:bg-[#e0d7cc]/40"
                                  >
                                    <Edit3 className="w-3 h-3 text-[#8b7355]" />
                                  </Button>
                                </div>
                              </div>
                              <p className="text-[#6d1f2c] font-poppins text-sm opacity-75 line-clamp-2">
                                {draft.content ? draft.content.replace(/<[^>]*>/g, '') : 'No content yet...'}
                              </p>
                              <div className="flex items-center justify-between mt-3 pt-3 border-t border-dashed border-[#e0d7cc]/50">
                                <span className="text-xs text-[#8b7355] font-poppins">
                                  Last edited: {formatDate(draft.updated_at || draft.created_at)}
                                </span>
                                <div className="flex items-center gap-2">
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      openDraftEditor(draft);
                                    }}
                                    className="h-7 px-3 text-xs font-poppins text-[#8b2635] hover:bg-[#8b2635]/10"
                                  >
                                    Continue Writing
                                  </Button>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      openDraftEditor(draft);
                                    }}
                                    className="h-7 px-3 text-xs font-poppins text-green-600 hover:bg-green-50"
                                  >
                                    Edit & Publish
                                  </Button>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      ) : (
                        <div className="text-center py-16">
                          <div className="w-24 h-24 bg-[#e0d7cc]/40 rounded-3xl flex items-center justify-center mx-auto mb-6">
                            <Edit3 className="w-12 h-12 text-[#8b7355]" />
                          </div>
                          <h3 className="font-cormorant font-bold text-[#2E0406] text-2xl mb-3">No drafts yet</h3>
                          <p className="text-[#8b7355] font-poppins mb-6 max-w-md mx-auto">
                            Start writing notes and save them as drafts before publishing.
                          </p>
                        </div>
                      )}
                    </TabsContent>
                  </Tabs>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Add Sub-Block Modal */}
        {showAddBlockModal && (
          <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-[9999]">
            <div className="bg-white/90 backdrop-blur-md border border-white/30 rounded-2xl shadow-2xl w-full max-w-md mx-4 p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-cormorant font-bold text-[#2E0406]">Add Folder</h2>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => setShowAddBlockModal(false)}
                  className="w-8 h-8 rounded-full hover:bg-[#8b2635]/10 transition-all duration-200"
                >
                  <X className="w-4 h-4 text-[#8b2635]" />
                </Button>
              </div>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-poppins font-medium text-[#2E0406] mb-2">
                    Block Name
                  </label>
                  <input
                    type="text"
                    value={newBlockName}
                    onChange={(e) => setNewBlockName(e.target.value)}
                    placeholder="Enter block name..."
                    className="w-full px-4 py-3 bg-white/60 border border-[#e0d7cc]/40 rounded-xl text-[#2E0406] placeholder-[#8b7355] font-poppins focus:outline-none focus:ring-2 focus:ring-[#8b2635]/30 focus:border-[#8b2635] transition-all duration-200"
                    autoFocus
                  />
                </div>
                
                <div className="flex gap-3 pt-4">
                  <Button
                    onClick={() => setShowAddBlockModal(false)}
                    variant="outline"
                    className="flex-1 border-[#e0d7cc] text-[#8b7355] hover:bg-[#e0d7cc]/20 rounded-xl h-12 font-poppins"
                  >
                    Cancel
                  </Button>
                  <Button
                    onClick={createSubBlock}
                    disabled={!newBlockName.trim()}
                    className="flex-1 bg-gradient-to-r from-[#8b2635] to-[#6d1f2c] hover:from-[#6d1f2c] hover:to-[#8b2635] text-white border-0 rounded-xl h-12 font-poppins disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Create Block
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Add Content Modal */}
        {showAddContentModal && (
          <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-[9999]">
            <div className="bg-white/90 backdrop-blur-md border border-white/30 rounded-2xl shadow-2xl w-full max-w-2xl mx-4 p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-cormorant font-bold text-[#2E0406]">Add Content</h2>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => setShowAddContentModal(false)}
                  className="w-8 h-8 rounded-full hover:bg-[#8b2635]/10 transition-all duration-200"
                >
                  <X className="w-4 h-4 text-[#8b2635]" />
                </Button>
              </div>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-poppins font-medium text-[#2E0406] mb-2">
                    Content Title
                  </label>
                  <input
                    type="text"
                    value={newContentTitle}
                    onChange={(e) => setNewContentTitle(e.target.value)}
                    placeholder="Enter content title..."
                    className="w-full px-4 py-3 bg-white/60 border border-[#e0d7cc]/40 rounded-xl text-[#2E0406] placeholder-[#8b7355] font-poppins focus:outline-none focus:ring-2 focus:ring-[#8b2635]/30 focus:border-[#8b2635] transition-all duration-200"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-poppins font-medium text-[#2E0406] mb-2">
                    Content
                  </label>
                  <textarea
                    value={newContentText}
                    onChange={(e) => setNewContentText(e.target.value)}
                    placeholder="Enter your content..."
                    rows={6}
                    className="w-full px-4 py-3 bg-white/60 border border-[#e0d7cc]/40 rounded-xl text-[#2E0406] placeholder-[#8b7355] font-poppins focus:outline-none focus:ring-2 focus:ring-[#8b2635]/30 focus:border-[#8b2635] transition-all duration-200 resize-none"
                  />
                </div>
                
                <div className="flex gap-3 pt-4">
                  <Button
                    onClick={() => setShowAddContentModal(false)}
                    variant="outline"
                    className="flex-1 border-[#e0d7cc] text-[#8b7355] hover:bg-[#e0d7cc]/20 rounded-xl h-12 font-poppins"
                  >
                    Cancel
                  </Button>
                  <Button
                    onClick={() => {
                      // TODO: Implement content creation
                      setShowAddContentModal(false);
                      setNewContentTitle("");
                      setNewContentText("");
                    }}
                    disabled={!newContentTitle.trim() || !newContentText.trim()}
                    className="flex-1 bg-gradient-to-r from-[#8b2635] to-[#6d1f2c] hover:from-[#6d1f2c] hover:to-[#8b2635] text-white border-0 rounded-xl h-12 font-poppins disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Add Content
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Add Folder Modal */}
        <AddFolderModal
          isOpen={showAddFolderModal}
          onClose={() => setShowAddFolderModal(false)}
          onCreateFolder={createFolder}
        />

        {/* File Viewer Modal */}
        {viewingFile && (
          <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-[9999] p-4">
            <div className="bg-white/95 backdrop-blur-md rounded-2xl shadow-2xl max-w-4xl max-h-[90vh] w-full overflow-hidden">
              {/* Modal Header */}
              <div className="flex items-center justify-between p-6 border-b border-[#e0d7cc]/40">
                <div>
                  <h2 className="text-xl font-cormorant font-bold text-[#2E0406]">
                    {viewingFile.title}
                  </h2>
                  <p className="text-sm text-[#8b7355] font-poppins mt-1">
                    {viewingFile.content_type.toUpperCase()} • {formatDate(viewingFile.created_at)}
                  </p>
                </div>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => setViewingFile(null)}
                  className="w-8 h-8 rounded-full hover:bg-[#8b2635]/10 transition-all duration-200"
                >
                  <X className="w-4 h-4 text-[#8b2635]" />
                </Button>
              </div>

              {/* Modal Content */}
              <div className="p-6 overflow-auto max-h-[calc(90vh-120px)]">
                {(() => {
                  const fileExtension = viewingFile.title.split('.').pop()?.toLowerCase();
                  const isPDF = fileExtension === 'pdf' || viewingFile.content.startsWith('data:application/pdf');
                  const isTextFile = ['txt', 'md', 'json', 'xml', 'csv', 'log', 'yaml', 'yml'].includes(fileExtension || '');
                  const isArchive = viewingFile.content_type === 'archive' || ['zip', 'rar', '7z', 'tar', 'gz', 'bz2', 'xz', 'zipx', 'cab', 'iso', 'dmg'].includes(fileExtension || '');

                  if (viewingFile.content_type === 'image') {
                    return (
                      <div className="flex justify-center">
                        <img
                          src={viewingFile.content}
                          alt={viewingFile.title}
                          className="max-w-full max-h-[60vh] object-contain rounded-xl"
                        />
                      </div>
                    );
                  } else if (viewingFile.content_type === 'text' || isTextFile) {
                    try {
                      const textContent = viewingFile.content.includes('base64,')
                        ? atob(viewingFile.content.split(',')[1])
                        : viewingFile.content;
                      return (
                        <div className="bg-[#f8f6f3] rounded-xl p-4 font-mono text-sm text-[#2E0406] whitespace-pre-wrap max-h-[60vh] overflow-auto">
                          {textContent}
                        </div>
                      );
                    } catch (error) {
                      return (
                        <div className="text-center py-8">
                          <div className="w-16 h-16 bg-[#8b2635]/10 rounded-xl flex items-center justify-center mx-auto mb-4">
                            <FileText className="w-8 h-8 text-[#8b2635]" />
                          </div>
                          <p className="text-[#8b7355] font-poppins">
                            Unable to decode text content.
                          </p>
                        </div>
                      );
                    }
                  } else if (isPDF) {
                    return (
                      <div className="flex justify-center">
                        <iframe
                          src={viewingFile.content}
                          className="w-full h-[60vh] rounded-xl border border-[#e0d7cc]/40"
                          title={viewingFile.title}
                        />
                      </div>
                    );
                  } else if (isArchive) {
                    return (
                      <div className="text-center py-8">
                        <div className="w-16 h-16 bg-[#8b2635]/10 rounded-xl flex items-center justify-center mx-auto mb-4">
                          <FileArchive className="w-8 h-8 text-[#8b2635]" />
                        </div>
                        <p className="text-[#8b7355] font-poppins mb-2">
                          Archive File
                        </p>
                        <p className="text-sm text-[#8b7355] font-poppins mb-2">
                          {viewingFile.title}
                        </p>
                        <p className="text-sm text-[#8b7355] font-poppins">
                          Type: {fileExtension?.toUpperCase() || 'Archive'}
                        </p>
                        <p className="text-sm text-[#8b7355] font-poppins">
                          Size: {Math.round(viewingFile.content.length / 1024)} KB
                        </p>
                        <div className="mt-4 p-3 bg-blue-50 rounded-lg">
                          <p className="text-sm text-blue-700">
                            💡 Archive files contain compressed folders and files. Extract to view contents.
                          </p>
                        </div>
                      </div>
                    );
                  } else if (viewingFile.content_type === 'video') {
                    return (
                      <div className="flex justify-center">
                        <video
                          src={viewingFile.content}
                          controls
                          className="max-w-full max-h-[60vh] rounded-xl"
                        >
                          Your browser does not support video playback.
                        </video>
                      </div>
                    );
                  } else if (viewingFile.content_type === 'audio') {
                    return (
                      <div className="flex justify-center p-8">
                        <audio
                          src={viewingFile.content}
                          controls
                          className="w-full max-w-md"
                        >
                          Your browser does not support audio playback.
                        </audio>
                      </div>
                    );
                  } else {
                    return (
                      <div className="text-center py-8">
                        <div className="w-16 h-16 bg-[#8b2635]/10 rounded-xl flex items-center justify-center mx-auto mb-4">
                          <FileText className="w-8 h-8 text-[#8b2635]" />
                        </div>
                        <p className="text-[#8b7355] font-poppins mb-2">
                          Preview not available for this file type.
                        </p>
                        <p className="text-sm text-[#8b7355] font-poppins">
                          File: {viewingFile.title}
                        </p>
                        <p className="text-sm text-[#8b7355] font-poppins">
                          Type: {fileExtension?.toUpperCase() || 'Unknown'}
                        </p>
                        <p className="text-sm text-[#8b7355] font-poppins">
                          Size: {Math.round(viewingFile.content.length / 1024)} KB
                        </p>
                      </div>
                    );
                  }
                })()}
              </div>
            </div>
          </div>
        )}

        {/* Error Notification */}
        {errorNotification.show && (
          <div className="fixed top-4 right-4 z-[10000] max-w-md">
            <div className={`
              bg-white border-l-4 rounded-lg shadow-lg p-4 transition-all duration-300 transform
              ${errorNotification.type === 'error' ? 'border-red-500' :
                errorNotification.type === 'warning' ? 'border-yellow-500' : 'border-green-500'}
            `}>
              <div className="flex items-start">
                <div className="flex-shrink-0">
                  {errorNotification.type === 'error' && (
                    <AlertTriangle className="w-5 h-5 text-red-500" />
                  )}
                  {errorNotification.type === 'warning' && (
                    <AlertTriangle className="w-5 h-5 text-yellow-500" />
                  )}
                  {errorNotification.type === 'success' && (
                    <div className="w-5 h-5 bg-green-500 rounded-full flex items-center justify-center">
                      <div className="w-2 h-2 bg-white rounded-full"></div>
                    </div>
                  )}
                </div>
                <div className="ml-3 flex-1">
                  <h3 className={`text-sm font-medium ${
                    errorNotification.type === 'error' ? 'text-red-800' :
                    errorNotification.type === 'warning' ? 'text-yellow-800' : 'text-green-800'
                  }`}>
                    {errorNotification.title}
                  </h3>
                  <p className={`mt-1 text-sm ${
                    errorNotification.type === 'error' ? 'text-red-700' :
                    errorNotification.type === 'warning' ? 'text-yellow-700' : 'text-green-700'
                  }`}>
                    {errorNotification.message}
                  </p>
                </div>
                <div className="ml-4 flex-shrink-0">
                  <button
                    onClick={() => setErrorNotification(prev => ({ ...prev, show: false }))}
                    className={`inline-flex rounded-md p-1.5 focus:outline-none focus:ring-2 focus:ring-offset-2 ${
                      errorNotification.type === 'error' ? 'text-red-500 hover:bg-red-100 focus:ring-red-500' :
                      errorNotification.type === 'warning' ? 'text-yellow-500 hover:bg-yellow-100 focus:ring-yellow-500' :
                      'text-green-500 hover:bg-green-100 focus:ring-green-500'
                    }`}
                  >
                    <X className="w-4 h-4" />
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Note Editor Drawer */}
        <NoteEditorDrawer
          isOpen={isEditorOpen}
          onClose={closeEditor}
          draft={editingDraft}
          onSave={handleDraftSave}
          onDelete={handleDraftDelete}
          blockId={blockId || ''}
        />
      </div>
    </>
  );
};
