import React, { useEffect, useImperativeHandle, forwardRef } from 'react';
import { useEditor, EditorContent } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import Link from '@tiptap/extension-link';
import Image from '@tiptap/extension-image';
import CodeBlock from '@tiptap/extension-code-block';
import { 
  Bold, 
  Italic, 
  Underline, 
  Strikethrough, 
  Code, 
  Heading1, 
  Heading2, 
  Heading3,
  List,
  ListOrdered,
  Quote,
  Link as LinkIcon,
  Image as ImageIcon,
  Undo,
  Redo
} from 'lucide-react';
import { Button } from './ui/button';

interface RichTextEditorProps {
  content: string;
  onChange: (content: string) => void;
  placeholder?: string;
  className?: string;
}

export interface RichTextEditorRef {
  focus: () => void;
  getHTML: () => string;
  setContent: (content: string) => void;
}

export const RichTextEditor = forwardRef<RichTextEditorRef, RichTextEditorProps>(
  ({ content, onChange, placeholder = "Start writing...", className = "" }, ref) => {
    const editor = useEditor({
      extensions: [
        StarterKit.configure({
          bulletList: {
            keepMarks: true,
            keepAttributes: false,
          },
          orderedList: {
            keepMarks: true,
            keepAttributes: false,
          },
        }),
        Link.configure({
          openOnClick: false,
          HTMLAttributes: {
            class: 'text-[#8b2635] underline hover:text-[#6d1f2c] transition-colors',
          },
        }),
        Image.configure({
          HTMLAttributes: {
            class: 'max-w-full h-auto rounded-lg',
          },
        }),
        CodeBlock.configure({
          HTMLAttributes: {
            class: 'bg-gray-100 rounded-lg p-4 font-mono text-sm',
          },
        }),
      ],
      content,
      onUpdate: ({ editor }) => {
        onChange(editor.getHTML());
      },
      editorProps: {
        attributes: {
          class: `prose prose-sm sm:prose lg:prose-lg xl:prose-2xl mx-auto focus:outline-none min-h-[300px] p-4 ${className}`,
        },
      },
    });

    useImperativeHandle(ref, () => ({
      focus: () => editor?.commands.focus(),
      getHTML: () => editor?.getHTML() || '',
      setContent: (content: string) => editor?.commands.setContent(content),
    }));

    useEffect(() => {
      if (editor && content !== editor.getHTML()) {
        editor.commands.setContent(content);
      }
    }, [content, editor]);

    if (!editor) {
      return null;
    }

    const addLink = () => {
      const url = window.prompt('Enter URL:');
      if (url) {
        editor.chain().focus().setLink({ href: url }).run();
      }
    };

    const addImage = () => {
      const url = window.prompt('Enter image URL:');
      if (url) {
        editor.chain().focus().setImage({ src: url }).run();
      }
    };

    return (
      <div className="border border-[#e0d7cc]/40 rounded-xl bg-white/60 overflow-hidden">
        {/* Toolbar */}
        <div className="border-b border-[#e0d7cc]/40 p-3 bg-[#f5f0e8]/50">
          <div className="flex flex-wrap gap-1">
            {/* Text Formatting */}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => editor.chain().focus().toggleBold().run()}
              className={`h-8 w-8 p-0 ${editor.isActive('bold') ? 'bg-[#8b2635] text-white' : 'hover:bg-[#e0d7cc]/40'}`}
            >
              <Bold className="w-4 h-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => editor.chain().focus().toggleItalic().run()}
              className={`h-8 w-8 p-0 ${editor.isActive('italic') ? 'bg-[#8b2635] text-white' : 'hover:bg-[#e0d7cc]/40'}`}
            >
              <Italic className="w-4 h-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => editor.chain().focus().toggleStrike().run()}
              className={`h-8 w-8 p-0 ${editor.isActive('strike') ? 'bg-[#8b2635] text-white' : 'hover:bg-[#e0d7cc]/40'}`}
            >
              <Strikethrough className="w-4 h-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => editor.chain().focus().toggleCode().run()}
              className={`h-8 w-8 p-0 ${editor.isActive('code') ? 'bg-[#8b2635] text-white' : 'hover:bg-[#e0d7cc]/40'}`}
            >
              <Code className="w-4 h-4" />
            </Button>

            <div className="w-px h-6 bg-[#e0d7cc]/40 mx-1" />

            {/* Headings */}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => editor.chain().focus().toggleHeading({ level: 1 }).run()}
              className={`h-8 w-8 p-0 ${editor.isActive('heading', { level: 1 }) ? 'bg-[#8b2635] text-white' : 'hover:bg-[#e0d7cc]/40'}`}
            >
              <Heading1 className="w-4 h-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => editor.chain().focus().toggleHeading({ level: 2 }).run()}
              className={`h-8 w-8 p-0 ${editor.isActive('heading', { level: 2 }) ? 'bg-[#8b2635] text-white' : 'hover:bg-[#e0d7cc]/40'}`}
            >
              <Heading2 className="w-4 h-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => editor.chain().focus().toggleHeading({ level: 3 }).run()}
              className={`h-8 w-8 p-0 ${editor.isActive('heading', { level: 3 }) ? 'bg-[#8b2635] text-white' : 'hover:bg-[#e0d7cc]/40'}`}
            >
              <Heading3 className="w-4 h-4" />
            </Button>

            <div className="w-px h-6 bg-[#e0d7cc]/40 mx-1" />

            {/* Lists */}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => editor.chain().focus().toggleBulletList().run()}
              className={`h-8 w-8 p-0 ${editor.isActive('bulletList') ? 'bg-[#8b2635] text-white' : 'hover:bg-[#e0d7cc]/40'}`}
            >
              <List className="w-4 h-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => editor.chain().focus().toggleOrderedList().run()}
              className={`h-8 w-8 p-0 ${editor.isActive('orderedList') ? 'bg-[#8b2635] text-white' : 'hover:bg-[#e0d7cc]/40'}`}
            >
              <ListOrdered className="w-4 h-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => editor.chain().focus().toggleBlockquote().run()}
              className={`h-8 w-8 p-0 ${editor.isActive('blockquote') ? 'bg-[#8b2635] text-white' : 'hover:bg-[#e0d7cc]/40'}`}
            >
              <Quote className="w-4 h-4" />
            </Button>

            <div className="w-px h-6 bg-[#e0d7cc]/40 mx-1" />

            {/* Media */}
            <Button
              variant="ghost"
              size="sm"
              onClick={addLink}
              className={`h-8 w-8 p-0 ${editor.isActive('link') ? 'bg-[#8b2635] text-white' : 'hover:bg-[#e0d7cc]/40'}`}
            >
              <LinkIcon className="w-4 h-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={addImage}
              className="h-8 w-8 p-0 hover:bg-[#e0d7cc]/40"
            >
              <ImageIcon className="w-4 h-4" />
            </Button>

            <div className="w-px h-6 bg-[#e0d7cc]/40 mx-1" />

            {/* Undo/Redo */}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => editor.chain().focus().undo().run()}
              disabled={!editor.can().undo()}
              className="h-8 w-8 p-0 hover:bg-[#e0d7cc]/40 disabled:opacity-50"
            >
              <Undo className="w-4 h-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => editor.chain().focus().redo().run()}
              disabled={!editor.can().redo()}
              className="h-8 w-8 p-0 hover:bg-[#e0d7cc]/40 disabled:opacity-50"
            >
              <Redo className="w-4 h-4" />
            </Button>
          </div>
        </div>

        {/* Editor Content */}
        <EditorContent 
          editor={editor} 
          className="prose prose-sm max-w-none [&_.ProseMirror]:outline-none [&_.ProseMirror]:min-h-[300px] [&_.ProseMirror]:p-4 [&_.ProseMirror]:text-[#2E0406] [&_.ProseMirror]:font-poppins"
        />
      </div>
    );
  }
);

RichTextEditor.displayName = 'RichTextEditor';
