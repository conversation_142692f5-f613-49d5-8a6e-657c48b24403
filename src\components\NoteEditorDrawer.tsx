import React, { useState, useRef, useEffect } from 'react';
import { Button } from './ui/button';
import { X, Save, FileText, Trash2 } from 'lucide-react';
import { RichTextEditor, RichTextEditorRef } from './RichTextEditor';
import { supabase } from '../lib/supabase';
import { useAuth } from '../hooks/useAuth';

interface BlockContent {
  id: string;
  block_id: string;
  title: string;
  content: string;
  content_type: 'text' | 'image' | 'video' | 'audio' | 'file' | 'archive';
  position: number;
  created_at: string;
  updated_at: string;
}

interface NoteEditorDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  draft: BlockContent | null;
  onSave: (draft: BlockContent) => void;
  onDelete?: (draftId: string) => void;
  blockId: string;
}

export const NoteEditorDrawer: React.FC<NoteEditorDrawerProps> = ({
  isOpen,
  onClose,
  draft,
  onSave,
  onDelete,
  blockId
}) => {
  const { user } = useAuth();
  const [title, setTitle] = useState('');
  const [content, setContent] = useState('');
  const [isSaving, setIsSaving] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [lastSaved, setLastSaved] = useState<Date | null>(null);
  const editorRef = useRef<RichTextEditorRef>(null);

  // Auto-save functionality
  const [autoSaveTimeout, setAutoSaveTimeout] = useState<NodeJS.Timeout | null>(null);

  useEffect(() => {
    if (draft) {
      setTitle(draft.title || 'Untitled Draft');
      setContent(draft.content || '');
    } else {
      setTitle('Untitled Draft');
      setContent('');
    }
  }, [draft]);

  // Auto-save after 2 seconds of inactivity
  useEffect(() => {
    if (autoSaveTimeout) {
      clearTimeout(autoSaveTimeout);
    }

    if (draft && (title !== draft.title || content !== draft.content)) {
      const timeout = setTimeout(() => {
        handleSave(false); // Silent save
      }, 2000);
      setAutoSaveTimeout(timeout);
    }

    return () => {
      if (autoSaveTimeout) {
        clearTimeout(autoSaveTimeout);
      }
    };
  }, [title, content]);

  const handleSave = async (showFeedback = true) => {
    if (!user || !blockId) return;

    setIsSaving(true);
    try {
      let savedDraft: BlockContent;

      if (draft) {
        // Update existing draft
        const { data, error } = await supabase
          .from('block_contents')
          .update({
            title: title.trim() || 'Untitled Draft',
            content: content,
            updated_at: new Date().toISOString()
          })
          .eq('id', draft.id)
          .select()
          .single();

        if (error) throw error;
        savedDraft = data;
      } else {
        // Create new draft
        const { data, error } = await supabase
          .from('block_contents')
          .insert([
            {
              block_id: blockId,
              user_id: user.uid,
              title: title.trim() || 'Untitled Draft',
              content: content,
              content_type: 'text'
            }
          ])
          .select()
          .single();

        if (error) throw error;
        savedDraft = data;
      }

      onSave(savedDraft);
      setLastSaved(new Date());
      
      if (showFeedback) {
        // Could add a toast notification here
        console.log('Draft saved successfully');
      }
    } catch (error) {
      console.error('Error saving draft:', error);
      // Could add error notification here
    } finally {
      setIsSaving(false);
    }
  };

  const handleDelete = async () => {
    if (!draft || !onDelete) return;

    if (!confirm('Are you sure you want to delete this draft? This action cannot be undone.')) {
      return;
    }

    setIsDeleting(true);
    try {
      const { error } = await supabase
        .from('block_contents')
        .delete()
        .eq('id', draft.id);

      if (error) throw error;

      onDelete(draft.id);
      onClose();
    } catch (error) {
      console.error('Error deleting draft:', error);
      // Could add error notification here
    } finally {
      setIsDeleting(false);
    }
  };

  const handlePublish = async () => {
    // For now, publishing just saves the draft
    // In the future, this could move the draft to published content
    await handleSave();
    onClose();
  };

  const formatLastSaved = () => {
    if (!lastSaved) return '';
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - lastSaved.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'Saved just now';
    if (diffInMinutes < 60) return `Saved ${diffInMinutes} minute${diffInMinutes > 1 ? 's' : ''} ago`;
    
    const diffInHours = Math.floor(diffInMinutes / 60);
    return `Saved ${diffInHours} hour${diffInHours > 1 ? 's' : ''} ago`;
  };

  if (!isOpen) return null;

  return (
    <>
      {/* Backdrop */}
      <div 
        className="fixed inset-0 bg-black/20 backdrop-blur-sm z-40 transition-opacity duration-300"
        onClick={onClose}
      />
      
      {/* Drawer */}
      <div className={`fixed right-0 top-0 h-full w-full max-w-4xl bg-white/95 backdrop-blur-md border-l border-[#e0d7cc]/40 shadow-2xl z-50 transform transition-transform duration-300 ease-in-out ${
        isOpen ? 'translate-x-0' : 'translate-x-full'
      }`}>
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-[#e0d7cc]/40 bg-[#f5f0e8]/50">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-[#8b2635]/10 rounded-xl flex items-center justify-center">
              <FileText className="w-5 h-5 text-[#8b2635]" />
            </div>
            <div>
              <h2 className="text-xl font-cormorant font-bold text-[#2E0406]">
                {draft ? 'Edit Draft' : 'New Note'}
              </h2>
              {lastSaved && (
                <p className="text-xs text-[#8b7355] font-poppins">
                  {formatLastSaved()}
                </p>
              )}
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            {draft && onDelete && (
              <Button
                variant="ghost"
                size="sm"
                onClick={handleDelete}
                disabled={isDeleting}
                className="text-red-600 hover:bg-red-50 hover:text-red-700"
              >
                <Trash2 className="w-4 h-4 mr-2" />
                {isDeleting ? 'Deleting...' : 'Delete'}
              </Button>
            )}
            
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleSave(true)}
              disabled={isSaving}
              className="border-[#e0d7cc] text-[#8b7355] hover:bg-[#e0d7cc]/20"
            >
              <Save className="w-4 h-4 mr-2" />
              {isSaving ? 'Saving...' : 'Save Draft'}
            </Button>
            
            <Button
              size="sm"
              onClick={handlePublish}
              disabled={isSaving}
              className="bg-gradient-to-r from-[#8b2635] to-[#6d1f2c] hover:from-[#6d1f2c] hover:to-[#8b2635] text-white border-0"
            >
              Publish
            </Button>
            
            <Button
              variant="ghost"
              size="icon"
              onClick={onClose}
              className="w-8 h-8 rounded-full hover:bg-[#8b2635]/10 transition-all duration-200"
            >
              <X className="w-4 h-4 text-[#8b2635]" />
            </Button>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-hidden flex flex-col">
          {/* Title Input */}
          <div className="p-6 border-b border-[#e0d7cc]/20">
            <input
              type="text"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              placeholder="Enter note title..."
              className="w-full text-2xl font-cormorant font-bold text-[#2E0406] placeholder-[#8b7355]/50 bg-transparent border-none outline-none"
            />
          </div>

          {/* Editor */}
          <div className="flex-1 overflow-auto p-6">
            <RichTextEditor
              ref={editorRef}
              content={content}
              onChange={setContent}
              placeholder="Start writing your note..."
              className="min-h-[calc(100vh-300px)]"
            />
          </div>
        </div>
      </div>
    </>
  );
};
